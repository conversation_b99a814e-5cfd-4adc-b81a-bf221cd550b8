package zmq

import (
	"encoding/json"
	"fmt"
	"log"

	internalzmq "github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/internal/zmq"
)

// HeartbeatHandlerAdapter 心跳处理器适配器
// 将心跳管理器适配为InstructionHandler接口，使其能够通过MessageRouter路由
type HeartbeatHandlerAdapter struct {
	heartbeatManager *internalzmq.HeartbeatManager
	robotNo          string
}

// NewHeartbeatHandlerAdapter 创建心跳处理器适配器
func NewHeartbeatHandlerAdapter(heartbeatManager *internalzmq.HeartbeatManager, robotNo string) *HeartbeatHandlerAdapter {
	return &HeartbeatHandlerAdapter{
		heartbeatManager: heartbeatManager,
		robotNo:          robotNo,
	}
}

// HandleInstruction 处理心跳指令（instruction=0）
func (h *HeartbeatHandlerAdapter) HandleInstruction(message *zmq.Message) (*zmq.Message, error) {
	log.Printf("💓 [心跳适配器] 收到心跳消息")
	log.Printf("   - 指令码: %d", message.Instruction)
	log.Printf("   - 时间戳: %v", message.TimeStamp)
	log.Printf("   - 机器人: %s", h.robotNo)

	// 验证是心跳消息
	if message.Instruction != zmq.InstructionHeartbeat {
		errMsg := fmt.Sprintf("期望心跳指令(0)，收到指令(%d)", message.Instruction)
		log.Printf("❌ [心跳适配器] %s", errMsg)
		return h.createErrorResponse(errMsg), nil
	}

	// 序列化消息为字节数组，调用心跳管理器的HandleMessage方法
	messageBytes, err := zmq.SerializeMessage(message)
	if err != nil {
		errMsg := fmt.Sprintf("序列化心跳消息失败: %v", err)
		log.Printf("❌ [心跳适配器] %s", errMsg)
		return h.createErrorResponse(errMsg), nil
	}

	// 调用心跳管理器处理
	responseBytes, err := h.heartbeatManager.HandleMessage(messageBytes)
	if err != nil {
		errMsg := fmt.Sprintf("心跳管理器处理失败: %v", err)
		log.Printf("❌ [心跳适配器] %s", errMsg)
		return h.createErrorResponse(errMsg), nil
	}

	// 反序列化响应
	response, err := zmq.DeserializeMessage(responseBytes)
	if err != nil {
		errMsg := fmt.Sprintf("反序列化心跳响应失败: %v", err)
		log.Printf("❌ [心跳适配器] %s", errMsg)
		return h.createErrorResponse(errMsg), nil
	}

	log.Printf("✅ [心跳适配器] 心跳处理成功")
	log.Printf("   - 响应指令: %d", response.Instruction)
	log.Printf("   - 响应代码: %v", response.Code)
	log.Printf("   - 响应消息: %s", response.Message)

	return response, nil
}

// GetInstructionCode 获取处理的指令码
func (h *HeartbeatHandlerAdapter) GetInstructionCode() int {
	return zmq.InstructionHeartbeat // 处理心跳指令 (0)
}

// GetDescription 获取处理器描述
func (h *HeartbeatHandlerAdapter) GetDescription() string {
	return "心跳处理器适配器 - 处理调度系统心跳消息"
}

// createErrorResponse 创建错误响应
func (h *HeartbeatHandlerAdapter) createErrorResponse(errorMsg string) *zmq.Message {
	log.Printf("📤 [心跳适配器] 创建错误响应: %s", errorMsg)
	
	response := zmq.NewMessage(zmq.InstructionReply, false, nil)
	response.Message = errorMsg
	
	return response
}

// GetStatus 获取适配器状态
func (h *HeartbeatHandlerAdapter) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"robotNo":          h.robotNo,
		"description":      h.GetDescription(),
		"instructionCode":  h.GetInstructionCode(),
		"adapterType":      "HeartbeatHandlerAdapter",
	}

	// 如果心跳管理器可用，添加其状态信息
	if h.heartbeatManager != nil {
		status["heartbeatManager"] = map[string]interface{}{
			"available":      true,
			"currentState":   h.heartbeatManager.GetCurrentState(),
			"lastHeartbeat":  h.heartbeatManager.GetLastHeartbeat().Format("2006-01-02 15:04:05"),
		}
	} else {
		status["heartbeatManager"] = map[string]interface{}{
			"available": false,
		}
	}

	return status
}