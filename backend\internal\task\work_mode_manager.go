package task

import (
	"fmt"
	"log"
	"sync"

	"github.com/user/agv_nav/internal/task/zmq"
)

// WorkMode 工作模式常量
const (
	WorkModeManual    = "manual"    // 手动模式
	WorkModeScheduled = "scheduled" // 调度模式
)

// WorkModeManager 工作模式管理器
type WorkModeManager struct {
	currentMode                    string                      // 当前工作模式
	isTaskRunning                  bool                        // 是否有任务正在运行
	currentTaskId                  string                      // 保存当前任务ID
	isWaitingForSchedulerInstruction bool                      // 是否正在等待调度指令
	taskLifecycleNotifier          *zmq.TaskLifecycleNotifier // 任务生命周期通知器
	mutex                          sync.RWMutex               // 读写锁保证线程安全
	zmqManager                     ZMQManager                  // ZMQ管理器接口（待实现）
	onModeChanged                  func(string)                // 模式变更回调函数
	startTaskFunc                  func([]string) error        // StartTask回调函数
}

// ZMQManager ZMQ管理器接口
type ZMQManager interface {
	Start() error
	Stop() error
	IsRunning() bool
	GetSchedulerInstructionHandler() zmq.SchedulerInstructionHandler
}

// NewWorkModeManager 创建工作模式管理器
func NewWorkModeManager() *WorkModeManager {
	// 注意：不直接使用配置中的工作模式，而是从手动模式开始
	// 这样可以确保在ZMQ管理器准备好后再正确切换到调度模式
	return &WorkModeManager{
		currentMode:                    WorkModeManual, // 始终从手动模式开始
		isTaskRunning:                  false,
		currentTaskId:                  "",
		isWaitingForSchedulerInstruction: false,
		taskLifecycleNotifier:          nil, // 后续在初始化时设置
		zmqManager:                     nil, // 后续在ZMQ管理器实现后注入
		startTaskFunc:                  nil, // 后续注入StartTask回调函数
	}
}

// GetCurrentMode 获取当前工作模式
func (w *WorkModeManager) GetCurrentMode() string {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	return w.currentMode
}

// IsManualMode 检查是否为手动模式
func (w *WorkModeManager) IsManualMode() bool {
	return w.GetCurrentMode() == WorkModeManual
}

// IsScheduledMode 检查是否为调度模式
func (w *WorkModeManager) IsScheduledMode() bool {
	return w.GetCurrentMode() == WorkModeScheduled
}

// IsTaskRunning 检查是否有任务正在运行
func (w *WorkModeManager) IsTaskRunning() bool {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	return w.isTaskRunning
}

// SetTaskRunning 设置任务运行状态
func (w *WorkModeManager) SetTaskRunning(running bool) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.isTaskRunning = running
	log.Printf("任务运行状态已更新: %v", running)
}

// SwitchToManualMode 切换到手动模式
func (w *WorkModeManager) SwitchToManualMode() error {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if w.currentMode == WorkModeManual {
		return nil // 已经是手动模式
	}

	log.Printf("正在切换到手动模式...")

	// 检查是否有任务正在运行
	if w.isTaskRunning {
		return fmt.Errorf("当前有任务正在运行，无法切换到手动模式")
	}

	// 停止ZMQ服务
	if w.zmqManager != nil && w.zmqManager.IsRunning() {
		if err := w.zmqManager.Stop(); err != nil {
			log.Printf("警告：停止ZMQ服务失败: %v", err)
		} else {
			log.Printf("ZMQ服务已停止")
		}
	}

	// 更新模式
	w.currentMode = WorkModeManual
	
	// 保存配置
	if err := w.saveConfigMode(WorkModeManual); err != nil {
		log.Printf("警告：保存工作模式配置失败: %v", err)
	}

	// 触发回调
	if w.onModeChanged != nil {
		w.onModeChanged(WorkModeManual)
	}

	log.Printf("已切换到手动模式")
	return nil
}

// SwitchToScheduledMode 切换到调度模式
func (w *WorkModeManager) SwitchToScheduledMode() error {
	log.Printf("🚨 DEBUG: SwitchToScheduledMode 方法被调用")
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if w.currentMode == WorkModeScheduled {
		log.Printf("🚨 DEBUG: 已经是调度模式，直接返回")
		return nil // 已经是调度模式
	}

	log.Printf("正在切换到调度模式...")

	// 检查是否有任务正在运行
	if w.isTaskRunning {
		return fmt.Errorf("当前有任务正在运行，无法切换到调度模式")
	}

	// 检查ZMQ配置是否启用
	config := GetConfig()
	if !config.ZMQConfig.Enabled {
		return fmt.Errorf("ZMQ功能未启用，无法切换到调度模式")
	}

	// 启动ZMQ服务
	if w.zmqManager != nil {
		isRunning := w.zmqManager.IsRunning()
		log.Printf("🔍 ZMQ管理器状态检查: IsRunning=%v", isRunning)
		if !isRunning {
			log.Printf("📤 开始启动ZMQ管理器...")
			if err := w.zmqManager.Start(); err != nil {
				return fmt.Errorf("启动ZMQ服务失败: %v", err)
			}
			log.Printf("✅ ZMQ服务已启动")
		} else {
			log.Printf("ℹ️  ZMQ管理器已在运行，跳过启动")
		}
	} else {
		log.Printf("警告：ZMQ管理器未初始化，调度功能可能不可用")
	}

	// 更新模式
	w.currentMode = WorkModeScheduled
	
	// 保存配置
	if err := w.saveConfigMode(WorkModeScheduled); err != nil {
		log.Printf("警告：保存工作模式配置失败: %v", err)
	}

	// 触发回调
	if w.onModeChanged != nil {
		w.onModeChanged(WorkModeScheduled)
	}

	log.Printf("已切换到调度模式")
	return nil
}

// ForceSwitch 强制切换模式（忽略正在运行的任务）
func (w *WorkModeManager) ForceSwitch(mode string) error {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if mode != WorkModeManual && mode != WorkModeScheduled {
		return fmt.Errorf("无效的工作模式: %s", mode)
	}

	log.Printf("强制切换到%s模式...", mode)

	// 强制停止当前任务
	if w.isTaskRunning {
		log.Printf("警告：强制终止正在运行的任务")
		w.isTaskRunning = false
	}

	// 根据目标模式执行相应操作
	if mode == WorkModeManual {
		// 停止ZMQ服务
		if w.zmqManager != nil && w.zmqManager.IsRunning() {
			w.zmqManager.Stop()
		}
	} else {
		// 启动ZMQ服务
		if w.zmqManager != nil && !w.zmqManager.IsRunning() {
			w.zmqManager.Start()
		}
	}

	// 更新模式
	w.currentMode = mode
	w.saveConfigMode(mode)

	// 触发回调
	if w.onModeChanged != nil {
		w.onModeChanged(mode)
	}

	log.Printf("已强制切换到%s模式", mode)
	return nil
}

// SetZMQManager 设置ZMQ管理器
func (w *WorkModeManager) SetZMQManager(zmqManager ZMQManager) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.zmqManager = zmqManager
	log.Printf("ZMQ管理器已设置")
}

// SetModeChangeCallback 设置模式变更回调函数
func (w *WorkModeManager) SetModeChangeCallback(callback func(string)) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.onModeChanged = callback
}

// InitializeMode 根据配置初始化工作模式
func (w *WorkModeManager) InitializeMode() error {
	config := GetConfig()
	log.Printf("根据配置初始化工作模式: %s", config.WorkMode)

	if config.WorkMode == WorkModeScheduled {
		return w.SwitchToScheduledMode()
	} else {
		return w.SwitchToManualMode()
	}
}

// saveConfigMode 保存工作模式到配置文件
func (w *WorkModeManager) saveConfigMode(mode string) error {
	config := GetConfig()
	config.WorkMode = mode
	return UpdateConfig(config)
}

// GetModeStatus 获取模式状态信息
func (w *WorkModeManager) GetModeStatus() map[string]interface{} {
	w.mutex.RLock()
	defer w.mutex.RUnlock()

	zmqRunning := false
	if w.zmqManager != nil {
		zmqRunning = w.zmqManager.IsRunning()
	}

	return map[string]interface{}{
		"currentMode":     w.currentMode,
		"isTaskRunning":   w.isTaskRunning,
		"currentTaskId":   w.currentTaskId,
		"zmqRunning":      zmqRunning,
		"canSwitchMode":   !w.isTaskRunning,
		"isManualMode":    w.currentMode == WorkModeManual,
		"isScheduledMode": w.currentMode == WorkModeScheduled,
	}
}

// SetCurrentTaskId 设置当前任务ID
func (w *WorkModeManager) SetCurrentTaskId(taskId string) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.currentTaskId = taskId
	log.Printf("💾 Current TaskId set to: %s", taskId)
}

// GetCurrentTaskId 获取当前任务ID
func (w *WorkModeManager) GetCurrentTaskId() string {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	return w.currentTaskId
}

// ClearCurrentTaskId 清空当前任务ID
func (w *WorkModeManager) ClearCurrentTaskId() {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	oldTaskId := w.currentTaskId
	w.currentTaskId = ""
	log.Printf("🗑️ TaskId cleared (was: %s)", oldTaskId)
}

// IsWaitingForSchedulerInstruction 检查是否正在等待调度指令
func (w *WorkModeManager) IsWaitingForSchedulerInstruction() bool {
	w.mutex.RLock()
	defer w.mutex.RUnlock()
	return w.isWaitingForSchedulerInstruction
}

// SetWaitingForSchedulerInstruction 设置等待调度指令状态
func (w *WorkModeManager) SetWaitingForSchedulerInstruction(waiting bool) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.isWaitingForSchedulerInstruction = waiting
	if waiting {
		log.Printf("⏳ 开始等待调度指令")
	} else {
		log.Printf("✅ 结束等待调度指令")
	}
}

// SetTaskLifecycleNotifier 设置任务生命周期通知器
func (w *WorkModeManager) SetTaskLifecycleNotifier(notifier *zmq.TaskLifecycleNotifier) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.taskLifecycleNotifier = notifier
	log.Printf("📢 Task lifecycle notifier set")
}

// SetStartTaskFunc 设置StartTask回调函数
func (w *WorkModeManager) SetStartTaskFunc(startTaskFunc func([]string) error) {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	w.startTaskFunc = startTaskFunc
	log.Printf("🚀 StartTask callback function set")
}

// NotifyTaskStart 发送任务开始通知（仅调度模式）
func (w *WorkModeManager) NotifyTaskStart(machine string) error {
	w.mutex.RLock()
	isScheduled := w.currentMode == WorkModeScheduled
	taskId := w.currentTaskId
	notifier := w.taskLifecycleNotifier
	w.mutex.RUnlock()

	if !isScheduled {
		log.Printf("⏭️ Skip task start notification (manual mode)")
		return nil // 非调度模式不发送
	}

	if taskId == "" {
		return fmt.Errorf("task ID is empty, cannot send task start notification")
	}

	if notifier == nil {
		return fmt.Errorf("task lifecycle notifier not initialized")
	}

	log.Printf("📤 Sending task start notification for machine: %s, taskId: %s", machine, taskId)
	return notifier.NotifyTaskStart(machine, taskId)
}

// NotifyTaskComplete 发送任务完成通知（仅调度模式）
func (w *WorkModeManager) NotifyTaskComplete(machine string) error {
	w.mutex.RLock()
	isScheduled := w.currentMode == WorkModeScheduled
	taskId := w.currentTaskId
	notifier := w.taskLifecycleNotifier
	w.mutex.RUnlock()

	if !isScheduled {
		log.Printf("⏭️ Skip task complete notification (manual mode)")
		return nil // 非调度模式不发送
	}

	if taskId == "" {
		return fmt.Errorf("task ID is empty, cannot send task complete notification")
	}

	if notifier == nil {
		return fmt.Errorf("task lifecycle notifier not initialized")
	}

	remark := fmt.Sprintf("Task completed for machine: %s", machine)
	log.Printf("📤 Sending task complete notification for taskId: %s, machine: %s", taskId, machine)
	return notifier.NotifyTaskComplete(taskId, remark)
}

// executeStartTask 执行StartTask函数
func (w *WorkModeManager) executeStartTask(laneNos []string) error {
	w.mutex.RLock()
	startTaskFunc := w.startTaskFunc
	w.mutex.RUnlock()

	if startTaskFunc == nil {
		log.Printf("❌ StartTask回调函数未设置")
		return fmt.Errorf("StartTask回调函数未设置")
	}

	log.Printf("🚀 开始执行StartTask，车道列表: %v", laneNos)
	err := startTaskFunc(laneNos)
	if err != nil {
		log.Printf("❌ StartTask执行失败: %v", err)
		return fmt.Errorf("StartTask执行失败: %v", err)
	}
	
	log.Printf("✅ StartTask执行成功")
	return nil
}

// WaitForSchedulerInstruction 等待调度系统的下一步指令
func (w *WorkModeManager) WaitForSchedulerInstruction() error {
	w.mutex.RLock()
	isScheduled := w.currentMode == WorkModeScheduled
	w.mutex.RUnlock()

	if !isScheduled {
		log.Printf("⏭️ 非调度模式，跳过等待调度指令")
		return nil
	}

	log.Printf("⏳ 开始等待调度系统下一步指令...")
	
	// 设置等待状态，防止200指令冲突
	w.SetWaitingForSchedulerInstruction(true)
	defer w.SetWaitingForSchedulerInstruction(false)
	
	// 获取调度指令处理器（需要从ZMQ管理器获取）
	if w.zmqManager == nil {
		return fmt.Errorf("ZMQ管理器未初始化")
	}
	
	// 实现等待循环逻辑
	for {
		log.Printf("🔄 等待调度指令循环开始...")
		
		// 获取调度指令处理器
		schedulerHandler := w.zmqManager.GetSchedulerInstructionHandler()
		
		// 等待调度指令（3分钟超时）
		result, err := schedulerHandler.WaitForInstruction()
		if err != nil {
			log.Printf("❌ 等待调度指令失败: %v", err)
			return err
		}
		
		// 处理不同的指令类型
		switch result.InstructionType {
		case 201: // 无任务
			log.Printf("🚫 收到无任务指令，返回主流程")
			return nil
			
		case 202: // 等待任务
			log.Printf("⏳ 收到等待任务指令，继续等待...")
			continue // 重新开始等待循环，重置3分钟超时
			
		case 203: // 继续任务
			if result.TaskInfo == nil {
				log.Printf("❌ 继续任务指令缺少任务信息")
				return fmt.Errorf("继续任务指令缺少任务信息")
			}
			
			log.Printf("🔄 收到继续任务指令，执行新任务")
			log.Printf("   - 新任务ID: %s", result.TaskInfo.TaskId)
			log.Printf("   - 车道列表: %v", result.TaskInfo.LaneNos)
			
			// 更新TaskId
			w.SetCurrentTaskId(result.TaskInfo.TaskId)
			
			// 调用StartTask执行新任务
			err := w.executeStartTask(result.TaskInfo.LaneNos)
			if err != nil {
				log.Printf("❌ 执行新任务失败: %v", err)
				return fmt.Errorf("执行新任务失败: %v", err)
			}
			
			// StartTask完成后，继续等待下一轮调度指令
			log.Printf("✅ 新任务执行完成，继续等待下一轮调度指令")
			continue
			
		default:
			log.Printf("⚠️ 收到未知指令类型: %d", result.InstructionType)
			continue
		}
	}
}